<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Configuration manager for Course Prerequisite Manager
 *
 * @package    tool_courseprereq
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace tool_courseprereq;

use tool_courseprereq\tool_courseprereq_utils;
use tool_lfxp\helpers\custom_fields\course\custom_course_field_category;
use local_ssystem\constants\custom_course_fields;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

global $CFG;

require_once($CFG->dirroot . '/admin/tool/courseprereq/installlib.php');

/**
 * Configuration manager class
 */
class tool_courseprereq_configmanager
{
    /**
     * Get all configuration settings
     *
     * @return array
     */
    public static function get_config()
    {

        $field = tool_courseprereq_utils::get_prereq_field();

        if (!$field) {
            return [];
        }

        $filters = tool_courseprereq_utils::get_course_filters();

        return [
            'enableplugin' => get_config(tool_courseprereq_constants::PLUGIN_NAME, 'enableplugin') ?: false,
            'blockmessage' => tool_courseprereq_utils::get_blockmessage(),
            'displaymode' => get_config(tool_courseprereq_constants::PLUGIN_NAME, 'displaymode') ?: tool_courseprereq_constants::DISPLAY_MODE_ALL,
            'filterhidden' => $filters['filterhidden'] ?? false,
            'filterfuture' => $filters['filterfuture'] ?? true,
            'filterexpired' => $filters['filterexpired'] ?? true,
        ];
    }

    /**
     * Save configuration settings
     *
     * @param object $data Form data
     * @return bool
     */
    public static function save_config($data) : bool
    {
        global $DB;

        $field = tool_courseprereq_utils::get_prereq_field();

        if (!$field) {
            return false;
        }

        $configdata = json_decode($field->configdata, true);

        set_config('enableplugin', !empty($data->enableplugin), tool_courseprereq_constants::PLUGIN_NAME);
        set_config('displaymode', $data->displaymode, tool_courseprereq_constants::PLUGIN_NAME);


        $configdata['filterhidden'] = $data->filterhidden;
        $configdata['filterfuture'] = $data->filterfuture;
        $configdata['filterexpired'] = $data->filterexpired;

        $field->configdata = json_encode($configdata);

        $DB->update_record('customfield_field', $field);

        if (isset($data->blockmessage)) {
            $message = $data->blockmessage['text'];
            set_config('blockmessage', $message, tool_courseprereq_constants::PLUGIN_NAME);
            set_config('blockmessage_format', $data->blockmessage['format'], tool_courseprereq_constants::PLUGIN_NAME);
        }

        return true;
    }

    public static function create_prereq_customfield()
    {
        $field = tool_courseprereq_utils::get_prereq_field();

        if($field){
            throw new moodle_exception('prereq_field_exists', 'tool_courseprereq');
        }

        $category = custom_course_field_category::get_by_name(custom_course_fields::CATEGORY_NAME);

        if ($category) {
            create_prereq_course_custom_fields($category);
        }

        return !!tool_courseprereq_utils::get_prereq_field();
    }
}
