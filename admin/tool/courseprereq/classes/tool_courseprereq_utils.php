<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace tool_courseprereq;

use tool_courseprereq\tool_courseprereq_constants;
use tool_courseprereq\persistent\tool_courseprereq;

defined('MOODLE_INTERNAL') || die();

/**
 * Class tool_courseprereq_utils
 *
 * @package    tool_courseprereq
 * @copyright  2025 YOUR NAME <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class tool_courseprereq_utils
{

    /**
     * Get the custom field record for the prerequisite field
     *
     * @return object|bool
     */
    public static function get_prereq_field(): object|bool
    {
        global $DB;

        return $DB->get_record(
            'customfield_field',
            [
                'shortname' => tool_courseprereq_constants::FIELD_NAME,
            ]
        );
    }

    /**
     * Get block message with format
     *
     * @return array
     */
    public static function get_blockmessage()
    {
        $text = get_config(tool_courseprereq_constants::PLUGIN_NAME, 'blockmessage');
        $format = get_config(tool_courseprereq_constants::PLUGIN_NAME, 'blockmessage_format');

        if (empty($text)) {
            $text = get_string('blockmessage_default', tool_courseprereq_constants::PLUGIN_NAME);
            $format = FORMAT_HTML;
        }

        return [
            'text' => $text,
            'format' => $format ?: FORMAT_HTML
        ];
    }

    /**
     * Get formatted block message for display
     *
     * @return string
     */
    public static function get_formatted_blockmessage()
    {
        $message = self::get_blockmessage();
        return format_text($message['text'], $message['format']);
    }

    /**
     * Check if plugin is enabled
     *
     * @return bool
     */
    public static function is_enabled()
    {
        return (bool) get_config(tool_courseprereq_constants::PLUGIN_NAME, 'enableplugin');
    }

    /**
     * Get display mode
     *
     * @return string
     */
    public static function get_displaymode()
    {
        return get_config(tool_courseprereq_constants::PLUGIN_NAME, 'displaymode') ?: tool_courseprereq_constants::DISPLAY_MODE_ALL;
    }

    /**
     * Get course filter settings
     *
     * @return array
     */
    public static function get_course_filters()
    {
        $field = tool_courseprereq_utils::get_prereq_field();

        if (!$field) {
            return [];
        }

        $configdata = json_decode($field->configdata, true);

        return [
            'filterhidden' => (bool) $configdata['filterhidden'],
            'filterfuture' => (bool) $configdata['filterfuture'],
            'filterexpired' => (bool) $configdata['filterexpired'],
        ];
    }


    /**
     * Get the status of all prerequisite courses for a given course and user
     *
     * @param int $courseid
     * @param int $userid
     * @return array array of course ids and their completion status
     */
    public static function get_prereq_course_statuses($courseid, $userid): array
    {
        global $DB;

        $filters = self::get_course_filters();

        $where = "WHERE t.courseid = :courseid";

        $params = [
            'courseid' => $courseid,
            'userid' => $userid
        ];

        if (!$filters['filterhidden']) {
            $where .= " AND c.visible = 1";
        }

        if (!$filters['filterfuture']) {
            $where .= " AND (c.startdate = 0 OR c.startdate <= :now1)";
            $params['now1'] = time();
        }

        if (!$filters['filterexpired']) {
            $where .= " AND (c.enddate = 0 OR c.enddate >= :now2)";
            $params['now2'] = time();
        }

        return $DB->get_records_sql(
            "SELECT 
                t.prereqcourseid as courseid,
                c.fullname as fullname,
                CASE
                    WHEN cc.timecompleted > 0 THEN 1
                    ELSE 0
                END as completed
            FROM {" . tool_courseprereq::TABLE . "} t
                JOIN {course} c ON c.id = t.prereqcourseid
                LEFT JOIN {course_completions} cc ON (cc.course = t.prereqcourseid AND cc.userid = :userid)
                {$where}
            ORDER BY completed DESC, fullname ASC
            ",
            $params
        );
    }

    /**
     * Check if a user has completed all prerequisite courses for a given course
     *
     * @param int $courseid
     * @param int $userid
     * @return bool true if the user has completed all prerequisite courses
     */
    public static function has_uncompleted_prereq_courses($courseid, $userid): bool
    {
        global $DB;

        $filters = self::get_course_filters();

        $where = "WHERE t.courseid = :courseid AND (cc.timecompleted IS NULL OR cc.timecompleted = 0)";

        $params = [
            'courseid' => $courseid,
            'userid' => $userid
        ];

        if (!$filters['filterhidden']) {
            $where .= " AND c.visible = 1";
        }

        if (!$filters['filterfuture']) {
            $where .= " AND (c.startdate = 0 OR c.startdate <= :now1)";
            $params['now1'] = time();
        }

        if (!$filters['filterexpired']) {
            $where .= " AND (c.enddate = 0 OR c.enddate >= :now2)";
            $params['now2'] = time();
        }

        return $DB->record_exists_sql(
            "SELECT 1
            FROM {" . tool_courseprereq::TABLE . "} t
                JOIN {course} c ON (c.id = t.prereqcourseid)
                LEFT JOIN {course_completions} cc ON (cc.course = t.prereqcourseid AND cc.userid = :userid)
                {$where}
            ",
            $params
        );
    }

    public static function course_has_prerequisites(int $courseid)
    {
        return tool_courseprereq::record_exists($courseid);
    }

    public static function get_displaymode_options()
    {
        return [
            tool_courseprereq_constants::DISPLAY_MODE_ALL => get_string('displaymode_all', 'tool_courseprereq'),
            tool_courseprereq_constants::DISPLAY_MODE_PENDING => get_string('displaymode_pending', 'tool_courseprereq'),
        ];
    }
}
