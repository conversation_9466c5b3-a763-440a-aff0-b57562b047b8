<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Configuration form for Course Prerequisite Manager
 *
 * @package    tool_courseprereq
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace tool_courseprereq\form;

use tool_courseprereq\tool_courseprereq_utils;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');

/**
 * Configuration form class
 */
class config_form extends \moodleform
{

    /**
     * Define the form elements
     */
    public function definition()
    {
        global $OUTPUT;

        $mform = $this->_form;

        $mform->addElement('header', 'settings',get_string('settings'));
        $mform->setExpanded('settings', true);

        $field = tool_courseprereq_utils::get_prereq_field();

        if($field) {

            $mform->addElement(
                'advcheckbox',
                'enableplugin',
                get_string('enableplugin', 'tool_courseprereq')
            );
            $mform->addHelpButton('enableplugin', 'enableplugin', 'tool_courseprereq');
            $mform->setType('enableplugin', PARAM_BOOL);
    
            $mform->addElement(
                'editor',
                'blockmessage',
                get_string('blockmessage', 'tool_courseprereq'),
                ['rows' => 5],
                [
                    'maxfiles' => 0,
                    'maxbytes' => 0,
                    'trusttext' => false,
                    'forcehttps' => false,
                    'subdirs' => false,
                    'changeformat' => 0,
                    'context' => \context_system::instance(),
                    'noclean' => false
                ]
            );
            $mform->addHelpButton('blockmessage', 'blockmessage', 'tool_courseprereq');
            $mform->setType('blockmessage', PARAM_RAW);

            $mform->addElement(
                'select',
                'displaymode',
                get_string('displaymode', 'tool_courseprereq'),
                tool_courseprereq_utils::get_displaymode_options()
            );

            $mform->addHelpButton('displaymode', 'displaymode', 'tool_courseprereq');
            $mform->setType('displaymode', PARAM_ALPHA);
    
            $mform->addElement('header', 'filter_settings', get_string('course_filters', 'tool_courseprereq'));
            $mform->setExpanded('filter_settings', true);
    
            $mform->addElement(
                'advcheckbox',
                'filterhidden',
                get_string('filterhidden', 'tool_courseprereq'),
                ''
            );
            $mform->addHelpButton('filterhidden', 'filterhidden', 'tool_courseprereq');
            $mform->setType('filterhidden', PARAM_BOOL);
    
            $mform->addElement(
                'advcheckbox',
                'filterfuture',
                get_string('filterfuture', 'tool_courseprereq'),
                ''
            );
            $mform->addHelpButton('filterfuture', 'filterfuture', 'tool_courseprereq');
            $mform->setType('filterfuture', PARAM_BOOL);
    
            $mform->addElement(
                'advcheckbox',
                'filterexpired',
                get_string('filterexpired', 'tool_courseprereq'),
                ''
            );
            $mform->addHelpButton('filterexpired', 'filterexpired', 'tool_courseprereq');
            $mform->setType('filterexpired', PARAM_BOOL);
    
            $action_msg = get_string('save_settings', 'tool_courseprereq');
        } 
        else {

            $mform->addElement('html', \html_writer::tag('div', 
                $OUTPUT->pix_icon('i/warning', '', 'moodle') . 
                get_string('prereq_field_not_found', 'tool_courseprereq'), 
                ['class' => 'alert alert-warning']
            ));

            $mform->addElement('hidden', 'createfield', true);
            $action_msg = get_string('create_field', 'tool_courseprereq');
        }

        $this->add_action_buttons(false, $action_msg);
    }

    /**
     * Validation
     *
     * @param array $data
     * @param array $files
     * @return array
     */
    public function validation($data, $files)
    {
        $errors = parent::validation($data, $files);

        // Validate block message
        if (isset($data['blockmessage']['text']) && empty(trim($data['blockmessage']['text']))) {
            $errors['blockmessage'] = get_string('required');
        }

        return $errors;
    }
}
