{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_courseprereq/prerequisitecourses

    Template for displaying prerequisite courses page.

    Context variables required for this template:
    * pagetitle - Page title string
    * returnurl - URL to return to course
    * backtocourse - Back to course button text
    * blockmessage - Block message content (HTML)
    * hascourses - Boolean indicating if there are courses to display
    * prerequisitecourses - Prerequisite courses title
    * coursedetails - Array of course objects with:
        - id - Course ID
        - fullname - Course full name
        - completed - Boolean completion status
        - url - Course URL
        - completedtext - Completed status text
        - notcompletedtext - Not completed status text
    * nocourses - No courses message

    Example context (json):
    {
        "returnurl": "http://example.com/course/view.php?id=1",
        "blockmessage": "<p>You need to complete the prerequisite courses before accessing this course.</p>",
        "hascourses": true,
        "coursedetails": [
            {
                "id": 2,
                "fullname": "Introduction to Programming",
                "completed": true,
                "url": "http://example.com/course/view.php?id=2"
            }
        ]
    }
}}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>{{#str}} prerequisitecourses, tool_courseprereq {{/str}}</h2>
            </div>
            <div class="d-flex"> 
                <div class="mr-4">
                    <span class="h6">{{#str}} course{{/str}}: </span>
                </div>
                <div class="">
                    <p class=""> {{coursefullname}} </p>
                </div>
            </div>
            {{#hascourses}}
                <div class="d-flex justify-content-center">
                    <div class="card col-lg-6 px-0">
                        <div class="card-header">
                            <h4>{{#str}} prerequisitecourses_title, tool_courseprereq , {{coursefullname}} {{/str}}</h4>
                        </div>
                        <div class="card-body">
                            <div>
                                {{{blockmessage}}}
                            </div>
                            <div class="list-group">
                                {{#coursedetails}}
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="mr-3">
                                                <a href="{{url}}" class="font-weight-bold text-decoration-none">
                                                    {{fullname}}
                                                </a>
                                            </div>
                                            <div >
                                                {{#completed}}
                                                    <span class="badge badge-success">
                                                        <i class="fa fa-check"></i>
                                                        {{#str}} course_completed, tool_courseprereq {{/str}}
                                                    </span>
                                                {{/completed}}
                                                {{^completed}}
                                                    <span class="badge badge-danger">
                                                        <i class="fa fa-times"></i>
                                                        {{#str}} course_not_completed, tool_courseprereq {{/str}}
                                                    </span>
                                                {{/completed}}
                                            </div>
                                        </div>
                                    </div>
                                {{/coursedetails}}
                            </div>
                        </div>
                    </div>
                </div>
            {{/hascourses}}
            {{^hascourses}}
                <div class="alert alert-info">
                    <p>{{#str}} nocourses {{/str}}</p>
                </div>
            {{/hascourses}}
            <div class="mt-4 text-center">
                <a href="{{returnurl}}" class="btn btn-primary btn-lg">
                    {{#str}} back {{/str}}
                </a>
            </div>
        </div>
    </div>
</div>
